param(
    [string]$CN = 'localhost'
)

$ErrorActionPreference = 'Stop'

# Environment checks
Write-Host "OpenSSL Certificate Generator" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

# OpenSSL Setup and Version Management
Write-Host ""
Write-Host "Checking OpenSSL installation..." -ForegroundColor Cyan

$opensslExe = (Get-Command openssl.exe -ErrorAction SilentlyContinue).Source
$targetVersion = "3.4.0"
$needsBuild = $false
$buildReason = ""

if ($opensslExe) {
    Write-Host "Found OpenSSL: $opensslExe" -ForegroundColor Green

    # Check current version
    try {
        $versionOutput = & $opensslExe version
        Write-Host "Current version: $versionOutput" -ForegroundColor White

        # Extract version number (e.g., "OpenSSL 3.1.0" -> "3.1.0")
        if ($versionOutput -match "OpenSSL (\d+\.\d+\.\d+)") {
            $currentVersion = $matches[1]

            # Compare versions (simple string comparison works for semantic versioning)
            if ([version]$currentVersion -lt [version]$targetVersion) {
                $needsBuild = $true
                $buildReason = "Newer version available ($targetVersion vs $currentVersion)"
                Write-Host "Update available: $buildReason" -ForegroundColor Yellow
            } else {
                Write-Host "OpenSSL is up to date." -ForegroundColor Green
            }
        } else {
            Write-Host "Could not determine OpenSSL version from output: $versionOutput" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "Warning: Could not check OpenSSL version: $_" -ForegroundColor Yellow
    }
} else {
    $needsBuild = $true
    $buildReason = "OpenSSL not found"
    Write-Host "OpenSSL not found on system." -ForegroundColor Yellow

    # Check for Perl if we need to build
    if (-not (Get-Command perl.exe -ErrorAction SilentlyContinue)) {
        Write-Error 'OpenSSL not found and Strawberry Perl is not installed. Please install Strawberry Perl or OpenSSL.'
        exit 1
    }
}

# Ask user for OpenSSL installation method
if ($needsBuild) {
    Write-Host ""
    Write-Host "OpenSSL Setup Required: $buildReason" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Choose installation method:" -ForegroundColor Cyan
    Write-Host "  1. Install FireDaemon OpenSSL via winget (recommended - fast & reliable)" -ForegroundColor White
    Write-Host "  2. Build OpenSSL from source (slower but latest version)" -ForegroundColor White
    Write-Host "  3. Skip OpenSSL setup (use existing installation)" -ForegroundColor White
    Write-Host ""

    do {
        $installChoice = Read-Host "Enter your choice (1/2/3)"
    } while ($installChoice -notmatch '^[123]$')

    switch ($installChoice) {
        "1" {
            # Install via winget
            Write-Host ""
            Write-Host "Installing FireDaemon OpenSSL via winget..." -ForegroundColor Yellow

            # Check if winget is available
            if (-not (Get-Command winget.exe -ErrorAction SilentlyContinue)) {
                Write-Error "winget is not available. Please install App Installer from Microsoft Store or choose option 2."
                exit 1
            }

            try {
                Write-Host "Running: winget install FireDaemon.OpenSSL" -ForegroundColor DarkGray
                & winget install FireDaemon.OpenSSL --accept-package-agreements --accept-source-agreements

                if ($LASTEXITCODE -eq 0) {
                    Write-Host "FireDaemon OpenSSL installed successfully!" -ForegroundColor Green

                    # Refresh environment and find OpenSSL
                    Write-Host "Refreshing environment variables..." -ForegroundColor DarkGray
                    $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")

                    # Try to find the new OpenSSL installation
                    $opensslExe = (Get-Command openssl.exe -ErrorAction SilentlyContinue).Source
                    if (-not $opensslExe) {
                        # Common FireDaemon OpenSSL locations
                        $commonPaths = @(
                            "${env:ProgramFiles}\FireDaemon OpenSSL 3\bin\openssl.exe",
                            "${env:ProgramFiles(x86)}\FireDaemon OpenSSL 3\bin\openssl.exe"
                        )
                        foreach ($path in $commonPaths) {
                            if (Test-Path $path) {
                                $opensslExe = $path
                                break
                            }
                        }
                    }

                    if ($opensslExe) {
                        Write-Host "Found OpenSSL at: $opensslExe" -ForegroundColor Green
                        $needsBuild = $false
                    } else {
                        Write-Warning "OpenSSL installed but not found in PATH. You may need to restart PowerShell."
                        Write-Host "Common location: ${env:ProgramFiles}\FireDaemon OpenSSL 3\bin\openssl.exe" -ForegroundColor Cyan
                    }
                } else {
                    Write-Error "winget installation failed with exit code $LASTEXITCODE"
                    exit 1
                }
            }
            catch {
                Write-Error "Failed to install OpenSSL via winget: $_"
                exit 1
            }
        }
        "2" {
            # Build from source
            Write-Host ""
            Write-Host "Building OpenSSL from source..." -ForegroundColor Yellow
            Write-Host "Note: This process can take 15-30 minutes and requires build tools." -ForegroundColor Cyan
            $needsBuild = $true
        }
        "3" {
            # Skip setup
            if ($opensslExe) {
                Write-Host "Continuing with existing OpenSSL installation." -ForegroundColor Cyan
                $needsBuild = $false
            } else {
                Write-Error "Cannot proceed without OpenSSL. Please choose option 1 or 2."
                exit 1
            }
        }
    }
} else {
    # Even if up to date, offer options
    Write-Host ""
    Write-Host "OpenSSL Options:" -ForegroundColor Cyan
    Write-Host "  1. Continue with current OpenSSL" -ForegroundColor White
    Write-Host "  2. Install/Update via winget" -ForegroundColor White
    Write-Host "  3. Force rebuild from source" -ForegroundColor White
    Write-Host ""

    $rebuildChoice = Read-Host "Enter your choice (1/2/3) [1]"
    if ([string]::IsNullOrWhiteSpace($rebuildChoice)) { $rebuildChoice = "1" }

    switch ($rebuildChoice) {
        "2" {
            # Install/update via winget
            Write-Host ""
            Write-Host "Installing/updating OpenSSL via winget..." -ForegroundColor Yellow
            if (Get-Command winget.exe -ErrorAction SilentlyContinue) {
                & winget install FireDaemon.OpenSSL --accept-package-agreements --accept-source-agreements
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "OpenSSL updated successfully!" -ForegroundColor Green
                    $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
                    $opensslExe = (Get-Command openssl.exe -ErrorAction SilentlyContinue).Source
                }
            } else {
                Write-Error "winget is not available."
                exit 1
            }
        }
        "3" {
            # Force rebuild
            $needsBuild = $true
            $buildReason = "User requested rebuild"
            Write-Host ""
            Write-Host "Force rebuilding OpenSSL from source..." -ForegroundColor Yellow
        }
        default {
            # Continue with current
            Write-Host "Continuing with current OpenSSL installation." -ForegroundColor Green
        }
    }
}

# Build OpenSSL if needed
if ($needsBuild) {
    Write-Host ""
    Write-Host "Building OpenSSL from source... ($buildReason)" -ForegroundColor Yellow

    $buildDir = "$PSScriptRoot\openssl-build"
    $prefix   = "$PSScriptRoot\openssl-local"
    $tarUrl   = 'https://www.openssl.org/source/openssl-3.4.0.tar.gz'
    $tarFile  = "$buildDir\openssl.tar.gz"
    $srcDir   = "$buildDir\openssl-3.4.0"

    New-Item -ItemType Directory -Force $buildDir,$prefix | Out-Null

    # Download source
    if (-not (Test-Path $tarFile)) {
        Write-Host "Downloading OpenSSL source from $tarUrl..." -ForegroundColor Yellow
        try {
            Invoke-WebRequest -Uri $tarUrl -OutFile $tarFile -UseBasicParsing
            Write-Host "Download completed." -ForegroundColor Green
        }
        catch {
            Write-Error "Failed to download OpenSSL source: $_"
            exit 1
        }
    }

    # Extract source
    if (-not (Test-Path $srcDir)) {
        Write-Host "Extracting OpenSSL source..." -ForegroundColor Yellow
        try {
            if (Get-Command tar.exe -ErrorAction SilentlyContinue) {
                & tar -xzf $tarFile -C $buildDir
            } else {
                # Fallback for older Windows versions
                Add-Type -AssemblyName System.IO.Compression.FileSystem
                $tempDir = "$buildDir\temp"
                New-Item -ItemType Directory -Force $tempDir | Out-Null
                [System.IO.Compression.ZipFile]::ExtractToDirectory($tarFile, $tempDir)
                Move-Item "$tempDir\*" $buildDir -Force
                Remove-Item $tempDir -Recurse -Force
            }
            Write-Host "Extraction completed." -ForegroundColor Green
        }
        catch {
            Write-Error "Failed to extract OpenSSL source: $_"
            exit 1
        }
    }

    # Check and install build tools
    Write-Host "Checking build tools..." -ForegroundColor Yellow

    $missingTools = @()
    $needsVSBuildTools = $false
    $needsNASM = $false

    if (-not (Get-Command cl.exe -ErrorAction SilentlyContinue)) {
        $missingTools += 'cl.exe (Visual Studio Build Tools)'
        $needsVSBuildTools = $true
    }
    if (-not (Get-Command nasm.exe -ErrorAction SilentlyContinue)) {
        $missingTools += 'nasm.exe (NASM assembler)'
        $needsNASM = $true
    }

    if ($missingTools.Count -gt 0) {
        Write-Host "Missing build tools: $($missingTools -join ', ')" -ForegroundColor Yellow
        $installChoice = Read-Host "Do you want to automatically install missing build tools? (Y/n)"

        if ($installChoice -notmatch '^[Nn]') {
            # Install missing tools
            if ($needsVSBuildTools) {
                Write-Host ""
                Write-Host "Visual Studio Build Tools Installation" -ForegroundColor Yellow
                Write-Host "=====================================" -ForegroundColor Yellow
                Write-Host "This will download and install Visual Studio Build Tools (~3GB download)" -ForegroundColor Cyan
                Write-Host "Installation typically takes 10-20 minutes depending on your internet speed." -ForegroundColor Cyan
                Write-Host ""

                $vsChoice = Read-Host "Continue with Visual Studio Build Tools installation? (Y/n)"
                if ($vsChoice -match '^[Nn]') {
                    Write-Host "Skipping Visual Studio Build Tools installation." -ForegroundColor Yellow
                    Write-Host "You can install manually later or use winget for OpenSSL instead." -ForegroundColor Cyan
                } else {
                    try {
                        # Download and install VS Build Tools
                        $vsBuildToolsUrl = "https://aka.ms/vs/17/release/vs_buildtools.exe"
                        $vsBuildToolsPath = "$env:TEMP\vs_buildtools.exe"

                        Write-Host ""
                        Write-Host "Step 1/3: Downloading Visual Studio Build Tools..." -ForegroundColor Yellow
                        Write-Host "URL: $vsBuildToolsUrl" -ForegroundColor DarkGray
                        Write-Host "Destination: $vsBuildToolsPath" -ForegroundColor DarkGray

                        # Download with progress
                        $webClient = New-Object System.Net.WebClient
                        $webClient.DownloadProgressChanged += {
                            param($sender, $e)
                            $percent = [math]::Round(($e.BytesReceived / $e.TotalBytesToReceive) * 100, 1)
                            Write-Progress -Activity "Downloading VS Build Tools" -Status "$percent% Complete" -PercentComplete $percent
                        }
                        $webClient.DownloadFileCompleted += {
                            Write-Progress -Activity "Downloading VS Build Tools" -Completed
                        }

                        $downloadTask = $webClient.DownloadFileTaskAsync($vsBuildToolsUrl, $vsBuildToolsPath)
                        $downloadTask.Wait()
                        $webClient.Dispose()

                        Write-Host "Download completed." -ForegroundColor Green

                        Write-Host ""
                        Write-Host "Step 2/3: Installing Visual Studio Build Tools..." -ForegroundColor Yellow
                        Write-Host "Components being installed:" -ForegroundColor DarkGray
                        Write-Host "  - C++ build tools workload" -ForegroundColor DarkGray
                        Write-Host "  - MSVC v143 compiler toolset" -ForegroundColor DarkGray
                        Write-Host "  - Windows 11 SDK" -ForegroundColor DarkGray
                        Write-Host ""
                        Write-Host "This process runs in the background and may take 10-20 minutes..." -ForegroundColor Cyan
                        Write-Host "You can monitor progress in Task Manager (look for vs_buildtools.exe)" -ForegroundColor Cyan

                        # Install with C++ build tools workload
                        $vsInstallArgs = @(
                            "--quiet"
                            "--wait"
                            "--add", "Microsoft.VisualStudio.Workload.VCTools"
                            "--add", "Microsoft.VisualStudio.Component.VC.Tools.x86.x64"
                            "--add", "Microsoft.VisualStudio.Component.Windows11SDK.22000"
                        )

                        Write-Host "Starting installation process..." -ForegroundColor DarkGray
                        $startTime = Get-Date

                        # Start the process and show periodic updates
                        $process = Start-Process -FilePath $vsBuildToolsPath -ArgumentList $vsInstallArgs -PassThru

                        # Monitor progress
                        $timeoutMinutes = 30
                        $checkInterval = 30 # seconds
                        $elapsed = 0

                        while (-not $process.HasExited -and $elapsed -lt ($timeoutMinutes * 60)) {
                            Start-Sleep $checkInterval
                            $elapsed += $checkInterval
                            $elapsedMinutes = [math]::Round($elapsed / 60, 1)
                            Write-Host "Installation in progress... ($elapsedMinutes minutes elapsed)" -ForegroundColor DarkGray
                        }

                        if (-not $process.HasExited) {
                            Write-Warning "Installation is taking longer than expected ($timeoutMinutes minutes)"
                            $continueWait = Read-Host "Continue waiting? (Y/n)"
                            if ($continueWait -notmatch '^[Nn]') {
                                Write-Host "Continuing to wait for installation..." -ForegroundColor Cyan
                                $process.WaitForExit()
                            } else {
                                Write-Host "Terminating installation process..." -ForegroundColor Yellow
                                $process.Kill()
                                throw "Installation cancelled by user"
                            }
                        }

                        $endTime = Get-Date
                        $totalMinutes = [math]::Round(($endTime - $startTime).TotalMinutes, 1)

                        if ($process.ExitCode -eq 0) {
                            Write-Host ""
                            Write-Host "Step 3/3: Installation completed successfully! (took $totalMinutes minutes)" -ForegroundColor Green

                            # Refresh environment to pick up new tools
                            Write-Host "Refreshing environment variables..." -ForegroundColor DarkGray
                            $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
                        } else {
                            Write-Warning "Visual Studio Build Tools installation completed with exit code: $($process.ExitCode)"
                            Write-Host "This may indicate a partial installation or warning condition." -ForegroundColor Cyan
                        }

                        # Clean up installer
                        Remove-Item $vsBuildToolsPath -Force -ErrorAction SilentlyContinue
                    }
                    catch {
                        Write-Error "Failed to install Visual Studio Build Tools: $_"
                        Write-Host "You can try installing manually from: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022" -ForegroundColor Cyan
                        exit 1
                    }
                }
            }

            if ($needsNASM) {
                Write-Host ""
                Write-Host "NASM Assembler Installation" -ForegroundColor Yellow
                Write-Host "===========================" -ForegroundColor Yellow

                try {
                    # Check if Chocolatey is available
                    if (Get-Command choco.exe -ErrorAction SilentlyContinue) {
                        Write-Host "Installing NASM via Chocolatey (recommended)..." -ForegroundColor Yellow
                        Write-Host "Running: choco install nasm -y" -ForegroundColor DarkGray

                        & choco install nasm -y
                        if ($LASTEXITCODE -eq 0) {
                            Write-Host "NASM installed successfully via Chocolatey." -ForegroundColor Green
                        } else {
                            throw "Chocolatey installation failed with exit code $LASTEXITCODE"
                        }
                    } else {
                        # Manual NASM installation
                        Write-Host "Chocolatey not found. Installing NASM manually..." -ForegroundColor Yellow
                        Write-Host ""

                        $nasmUrl = "https://www.nasm.us/pub/nasm/releasebuilds/2.16.01/win64/nasm-2.16.01-win64.zip"
                        $nasmZip = "$env:TEMP\nasm.zip"
                        $nasmDir = "$env:ProgramFiles\NASM"

                        Write-Host "Step 1/4: Downloading NASM 2.16.01..." -ForegroundColor DarkGray
                        Write-Host "URL: $nasmUrl" -ForegroundColor DarkGray
                        Invoke-WebRequest -Uri $nasmUrl -OutFile $nasmZip -UseBasicParsing
                        Write-Host "Download completed." -ForegroundColor Green

                        Write-Host "Step 2/4: Extracting NASM..." -ForegroundColor DarkGray
                        Add-Type -AssemblyName System.IO.Compression.FileSystem
                        [System.IO.Compression.ZipFile]::ExtractToDirectory($nasmZip, $env:TEMP)
                        Write-Host "Extraction completed." -ForegroundColor Green

                        Write-Host "Step 3/4: Installing to Program Files..." -ForegroundColor DarkGray
                        $extractedDir = Get-ChildItem "$env:TEMP\nasm-*" | Select-Object -First 1
                        if (-not (Test-Path $nasmDir)) {
                            New-Item -ItemType Directory -Path $nasmDir -Force | Out-Null
                        }
                        Copy-Item "$($extractedDir.FullName)\*" $nasmDir -Recurse -Force
                        Write-Host "Files copied to: $nasmDir" -ForegroundColor Green

                        Write-Host "Step 4/4: Adding to system PATH..." -ForegroundColor DarkGray
                        $currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
                        if ($currentPath -notlike "*$nasmDir*") {
                            [Environment]::SetEnvironmentVariable("Path", "$currentPath;$nasmDir", "Machine")
                            $env:Path += ";$nasmDir"
                            Write-Host "Added to PATH: $nasmDir" -ForegroundColor Green
                        } else {
                            Write-Host "Already in PATH: $nasmDir" -ForegroundColor Green
                        }

                        Write-Host "NASM installation completed successfully!" -ForegroundColor Green

                        # Clean up
                        Write-Host "Cleaning up temporary files..." -ForegroundColor DarkGray
                        Remove-Item $nasmZip -Force -ErrorAction SilentlyContinue
                        Remove-Item $extractedDir.FullName -Recurse -Force -ErrorAction SilentlyContinue
                    }
                }
                catch {
                    Write-Error "Failed to install NASM: $_"
                    Write-Host "You can install NASM manually from: https://www.nasm.us/pub/nasm/releasebuilds/" -ForegroundColor Cyan
                    exit 1
                }
            }

            # Re-check tools after installation
            Write-Host "Verifying installed tools..." -ForegroundColor Yellow
            $stillMissing = @()
            if (-not (Get-Command cl.exe -ErrorAction SilentlyContinue)) { $stillMissing += 'cl.exe' }
            if (-not (Get-Command nasm.exe -ErrorAction SilentlyContinue)) { $stillMissing += 'nasm.exe' }

            if ($stillMissing.Count -gt 0) {
                Write-Warning "Some tools are still not available: $($stillMissing -join ', ')"
                Write-Host "You may need to restart your PowerShell session or reboot to refresh environment variables." -ForegroundColor Cyan
                $continueChoice = Read-Host "Continue anyway? (y/N)"
                if ($continueChoice -notmatch '^[Yy]') {
                    exit 1
                }
            } else {
                Write-Host "All build tools are now available." -ForegroundColor Green
            }
        } else {
            Write-Error "Cannot proceed without required build tools: $($missingTools -join ', ')"
            exit 1
        }
    } else {
        Write-Host "All required build tools are available." -ForegroundColor Green
    }

    # Build OpenSSL
    Push-Location $srcDir
    try {
        Write-Host "Configuring OpenSSL build..." -ForegroundColor Yellow
        perl Configure VC-WIN64A --prefix=$prefix --openssldir=$prefix\ssl
        
        Write-Host "Building OpenSSL (this may take 10-15 minutes)..." -ForegroundColor Yellow
        nmake
        
        Write-Host "Running OpenSSL tests..." -ForegroundColor Yellow
        nmake test
        
        Write-Host "Installing OpenSSL..." -ForegroundColor Yellow
        nmake install
        
        Write-Host "OpenSSL build completed successfully." -ForegroundColor Green
    }
    catch {
        Write-Error "OpenSSL build failed: $_"
        exit 1
    }
    finally { 
        Pop-Location 
    }

    $opensslExe = "$prefix\bin\openssl.exe"
    if (-not (Test-Path $opensslExe)) { 
        Write-Error 'Build completed but openssl.exe not found at expected location.'
        exit 1
    }
}

Write-Host ""
Write-Host "Using OpenSSL: $opensslExe" -ForegroundColor Green

# Test OpenSSL
try {
    $version = & $opensslExe version
    Write-Host "OpenSSL version: $version" -ForegroundColor Green
}
catch {
    Write-Error "OpenSSL executable test failed: $_"
    exit 1
}

# Get user input for certificate creation
Write-Host ""
Write-Host "Certificate Configuration" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

do {
    $Name = Read-Host "Enter certificate base name (required)"
} while ([string]::IsNullOrWhiteSpace($Name))

#$PasswordInput = Read-Host "Enter password (press Enter for default 'Jabil12345')"
#$Password = if ([string]::IsNullOrWhiteSpace($PasswordInput)) { 'Jabil12345' } else { $PasswordInput }

Write-Host ""
Write-Host "Configuration Summary:" -ForegroundColor Cyan
Write-Host "  Certificate name: $Name" -ForegroundColor White
Write-Host "  Common Name: $CN" -ForegroundColor White
Write-Host "  Password: $('*' * $Password.Length)" -ForegroundColor White

# Create certificates
try {
    Write-Host ""
    Write-Host "Creating certificate files..." -ForegroundColor Yellow
    
    # Create private key and self-signed certificate
    Write-Host "Creating private key and certificate..." -ForegroundColor DarkGray
    & $opensslExe req -x509 -newkey rsa:2048 -days 730 -nodes -keyout "${Name}.key" -out "${Name}.crt" -subj "/C=US/ST=State/L=City/O=Organization/OU=Unit/CN=$CN"
    if ($LASTEXITCODE -ne 0) { throw "Failed to create certificate" }

    # Create PKCS#12 bundle
    Write-Host "Creating PKCS#12 bundle..." -ForegroundColor DarkGray
    & $opensslExe pkcs12 -export -out "${Name}.pfx" -inkey "${Name}.key" -in "${Name}.crt" #-password "pass:$Password"
    if ($LASTEXITCODE -ne 0) { throw "Failed to create PKCS#12 bundle" }

    Write-Host ""
    Write-Host "Certificate files created successfully!" -ForegroundColor Green
    Write-Host "Location: $(Get-Location)" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "Files created:" -ForegroundColor Cyan
    Get-ChildItem "${Name}.*" | ForEach-Object { 
        $size = [math]::Round($_.Length / 1KB, 1)
        Write-Host "  * $($_.Name) - $size KB" -ForegroundColor White 
    }
    
    Write-Host ""
    Write-Host "Certificate details:" -ForegroundColor Cyan
    Write-Host "  * Common Name: $CN" -ForegroundColor White
    Write-Host "  * Valid for: 730 days - 2 years" -ForegroundColor White
    Write-Host "  * Key size: 2048 bits RSA" -ForegroundColor White
    Write-Host "  * Self-signed X.509 certificate" -ForegroundColor White
}
catch {
    Write-Error "Certificate creation failed: $_"
    exit 1
}

Write-Host ""
Write-Host "Process completed successfully!" -ForegroundColor Green
