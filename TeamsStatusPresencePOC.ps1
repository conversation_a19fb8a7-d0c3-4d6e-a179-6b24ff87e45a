# Ensure the Microsoft.Graph module is installed
if (-not (Get-Module -ListAvailable -Name Microsoft.Graph)) {
    Write-Host "Microsoft.Graph module not found. Please install it by running: Install-Module Microsoft.Graph -Scope CurrentUser" -ForegroundColor Yellow
    exit
}

$ClientID = "e626d656-8683-401f-8058-b80c6d60817a"
$ClientSecret = "****************************************"
$TenantID = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"

# Configuration
$ForceUpdatePresence = $false  # Set to $true to update presence regardless of OOO status

# Function to get presence selection based on current time (GMT+8)
function Get-PresenceSelection {
    param (
        [string]$userPrincipalName
    )

    $utcNow = [DateTime]::UtcNow
    $gmt8TimeZone = $null
    $timezoneIds = @("Singapore Standard Time", "Asia/Singapore", "Asia/Kuala_Lumpur")

    foreach ($tzId in $timezoneIds) {
        try {
            $gmt8TimeZone = [System.TimeZoneInfo]::FindSystemTimeZoneById($tzId)
            break
        } catch {
            continue
        }
    }

    if ($null -eq $gmt8TimeZone) {
        Write-Host "Timezone lookup failed, using manual GMT+8 calculation" -ForegroundColor Yellow
        $currentTimeGMT8 = $utcNow.AddHours(8)
    } else {
        $currentTimeGMT8 = [System.TimeZoneInfo]::ConvertTimeFromUtc($utcNow, $gmt8TimeZone)
    }

    Write-Host "Server time (UTC): $($utcNow.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Gray
    Write-Host "Current time (GMT+8): $($currentTimeGMT8.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan

    if ($userPrincipalName -ieq "<EMAIL>") {
        $workStartTime = [DateTime]::ParseExact("06:00", "HH:mm", $null)
        $workEndTime = [DateTime]::ParseExact("14:15", "HH:mm", $null)
    } else {
        $workStartTime = [DateTime]::ParseExact("08:00", "HH:mm", $null)
        $workEndTime = [DateTime]::ParseExact("16:15", "HH:mm", $null)
    }

    $currentTime = $currentTimeGMT8.TimeOfDay

    if ($currentTime -ge $workStartTime.TimeOfDay -and $currentTime -le $workEndTime.TimeOfDay) {
        Write-Host "$userPrincipalName is within working hours" -ForegroundColor Green
        return @{
            Availability = "Busy"
            Activity = "InAConferenceCall"
            Description = "Busy - In a conference call (Working Hours)"
        }
    } else {
        Write-Host "$userPrincipalName is outside working hours" -ForegroundColor Yellow
        return @{
            Availability = "Away"
            Activity = "Away"
            Description = "Away - Outside working hours"
        }
    }
}

function Get-ExpirationDuration {
    return "PT4H"  # 4 hours maximum
}

try {
    $secureClientSecret = ConvertTo-SecureString $ClientSecret -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential($ClientID, $secureClientSecret)
    Connect-MgGraph -Credential $credential -TenantId $TenantID
    Write-Host "Successfully connected to Microsoft Graph API." -ForegroundColor Green
} catch {
    Write-Host "Failed to connect to Microsoft Graph API. Error: $_" -ForegroundColor Red
    exit
}

$upns = @(
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
)

$users = @()
foreach ($upn in $upns) {
    try {
        $uri = "https://graph.microsoft.com/v1.0/users/$upn" + '?$select=id,userPrincipalName'
        $user = Invoke-MgGraphRequest -Method GET -Uri $uri

        $users += @{
            id = $user.id
            userPrincipalName = $user.userPrincipalName
        }
        Write-Host "Found user: $($user.userPrincipalName)" -ForegroundColor Gray
    } catch {
        Write-Host "Could not find user $upn. Error: $_" -ForegroundColor Red
    }
}

if ($users.Count -eq 0) {
    Write-Host "No users found. Exiting." -ForegroundColor Red
    exit
}

$now = Get-Date
$todayStart = $now.Date.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
$todayEnd = $now.Date.AddDays(1).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")

Write-Host "`n--- Checking for Out of Office ---" -ForegroundColor Cyan
$usersWithoutOOO = [System.Collections.Generic.List[PSCustomObject]]::new()

foreach ($user in $users) {
    $userId = $user.id
    $upn = $user.userPrincipalName
    $isCurrentlyOOO = $false

    $calendarViewUrl = "https://graph.microsoft.com/v1.0/users/$userId/calendarview?startdatetime=$todayStart&enddatetime=$todayEnd" + '&$select=showAs,start,end,subject'

    try {
        $calendarEvents = Invoke-MgGraphRequest -Method GET -Uri $calendarViewUrl

        foreach ($event in $calendarEvents.value) {
            if ($event.showAs -eq 'oof') {
                $eventStart = if ($event.start.dateTime) {
                    $startTime = [DateTime]::Parse($event.start.dateTime)
                    if ($event.start.timeZone -eq "UTC" -or $event.start.dateTime.EndsWith("Z")) {
                        $startTime = $startTime.ToLocalTime()
                    }
                    $startTime
                } else { [DateTime]::Parse($event.start.date).Date }

                $eventEnd = if ($event.end.dateTime) {
                    $endTime = [DateTime]::Parse($event.end.dateTime)
                    if ($event.end.timeZone -eq "UTC" -or $event.end.dateTime.EndsWith("Z")) {
                        $endTime = $endTime.ToLocalTime()
                    }
                    $endTime
                } else { [DateTime]::Parse($event.end.date).Date.AddDays(1).AddSeconds(-1) }

                if ($now -ge $eventStart -and $now -le $eventEnd) {
                    $isCurrentlyOOO = $true
                    Write-Host "$upn is currently Out of Office" -ForegroundColor Yellow
                    break
                }
            }
        }

        if (-not $isCurrentlyOOO) {
            Write-Host "$upn is available (not OOO)" -ForegroundColor Green
            $usersWithoutOOO.Add([PSCustomObject]@{
                id = $userId
                userPrincipalName = $upn
            })
        }
    } catch {
        Write-Host "Could not check calendar for $upn - assuming available" -ForegroundColor Gray
        $usersWithoutOOO.Add([PSCustomObject]@{
            id = $userId
            userPrincipalName = $upn
        })
    }
}

Write-Host "`n--- Current User Presences ---" -ForegroundColor Cyan
$userIds = $users | ForEach-Object { $_.id }
$presenceBody = @{ ids = $userIds } | ConvertTo-Json

try {
    $presences = Invoke-MgGraphRequest -Method POST -Uri 'https://graph.microsoft.com/v1.0/communications/getPresencesByUserId' -Body $presenceBody -ContentType 'application/json'

    $presences.value | ForEach-Object {
        $currentUserId = $_.id
        $currentUser = $users | Where-Object { $_.id -eq $currentUserId }
        Write-Host "• $($currentUser.userPrincipalName): $($_.availability) - $($_.activity)" -ForegroundColor White
    }
} catch {
    Write-Host "Could not retrieve current presences: $_" -ForegroundColor Red
}

Write-Host "`n--- Setting New Presence ---" -ForegroundColor Cyan
$usersToUpdate = if ($ForceUpdatePresence) { $users } else { $usersWithoutOOO }

if ($usersToUpdate.Count -eq 0) {
    Write-Host "No users need presence updates." -ForegroundColor Yellow
} else {
    foreach ($user in $usersToUpdate) {
        $userId = $user.id
        $upn = $user.userPrincipalName
        $selectedPresence = Get-PresenceSelection -userPrincipalName $upn
        $selectedDuration = Get-ExpirationDuration

        try {
            $clearPresenceBody = @{ sessionId = $ClientID } | ConvertTo-Json
            try {
				Write-Host "$ClearPresenceBody"
                Invoke-MgGraphRequest -Method POST -Uri "https://graph.microsoft.com/v1.0/users/$userId/presence/clearPresence" -Body $clearPresenceBody -ContentType 'application/json'
                Invoke-MgGraphRequest -Method POST -Uri "https://graph.microsoft.com/v1.0/users/$userId/presence/clearUserPreferredPresence" -Body "{}" -ContentType 'application/json'
            } catch {
            }

            $setPresenceBody = @{
                sessionId = $ClientID
                availability = $selectedPresence.Availability
                activity = $selectedPresence.Activity
                expirationDuration = $selectedDuration
            } | ConvertTo-Json
			
			Write-Host "$setPresenceBody"
            Invoke-MgGraphRequest -Method POST -Uri "https://graph.microsoft.com/v1.0/users/$userId/presence/setPresence" -Body $setPresenceBody -ContentType 'application/json'
			
            Write-Host "✓ Updated presence for $upn -  $($selectedPresence.Description)" -ForegroundColor Green

        } catch {
            Write-Host "✗ Failed to set presence for $upn -  $_" -ForegroundColor Red
        }
    }
}

Write-Host "`nScript completed.`n" -ForegroundColor Cyan
Disconnect-MgGraph
