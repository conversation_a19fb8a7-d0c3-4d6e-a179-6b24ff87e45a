# ===== TEST: Main Script PublisherName Fix Verification =====
# Purpose: Test the fixed main script logic with beta endpoint for PublisherName

# ===== CONFIGURATION =====
$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
    TestLimit = 10  # Test with first 10 service principals
}

Write-Host "=== Main Script PublisherName Fix Verification ===" -ForegroundColor Cyan
Write-Host "Testing the fixed logic with beta endpoint" -ForegroundColor Yellow

# ===== AUTHENTICATION =====
Write-Host "Authenticating to Microsoft Graph..." -ForegroundColor Yellow

$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

try {
    $connection = Invoke-RestMethod `
        -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" `
        -Method POST `
        -Body $body

    $token = $connection.access_token
    Write-Host "Authentication successful" -ForegroundColor Green
}
catch {
    Write-Host "Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ===== HELPER FUNCTIONS (from main script) =====
function Invoke-GraphRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri
    )

    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    try {
        return Invoke-RestMethod -Uri $Uri -Method GET -Headers $headers
    }
    catch {
        Write-Host "Graph API Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Get-ServicePrincipalOwnerInfo {
    param([object]$ServicePrincipal)

    $ownerInfo = @{
        OwnerNames = @()
        OwnerTypes = @()
        OwnerCount = 0
        OwnerNamesString = "N/A"
        OwnerTypesString = "N/A"
    }

    if ($ServicePrincipal.owners -and $ServicePrincipal.owners.Count -gt 0) {
        foreach ($owner in $ServicePrincipal.owners) {
            try {
                $ownerType = "Unknown"
                $ownerName = "Unknown"

                if ($owner.'@odata.type' -eq '#microsoft.graph.user') {
                    $ownerType = "User"
                    $ownerName = if ($owner.userPrincipalName) { $owner.userPrincipalName } elseif ($owner.displayName) { $owner.displayName } else { $owner.id }
                }
                elseif ($owner.'@odata.type' -eq '#microsoft.graph.servicePrincipal') {
                    $ownerType = "ServicePrincipal"
                    $ownerName = if ($owner.displayName) { $owner.displayName } elseif ($owner.appId) { $owner.appId } else { $owner.id }
                }
                else {
                    $ownerType = if ($owner.'@odata.type') { $owner.'@odata.type'.Replace('#microsoft.graph.', '') } else { "Unknown" }
                    $ownerName = if ($owner.displayName) { $owner.displayName } elseif ($owner.userPrincipalName) { $owner.userPrincipalName } else { $owner.id }
                }

                $ownerInfo.OwnerNames += $ownerName
                $ownerInfo.OwnerTypes += $ownerType
            }
            catch {
                $ownerInfo.OwnerNames += "Error processing owner"
                $ownerInfo.OwnerTypes += "Error"
            }
        }

        $ownerInfo.OwnerCount = $ownerInfo.OwnerNames.Count
        $ownerInfo.OwnerNamesString = $ownerInfo.OwnerNames -join "; "
        $ownerInfo.OwnerTypesString = $ownerInfo.OwnerTypes -join "; "
    }

    return $ownerInfo
}

# ===== TEST THE FIXED LOGIC =====
Write-Host "`nTesting fixed service principals query with beta endpoint..." -ForegroundColor Yellow

# Use the FIXED URI from the main script (now using beta endpoint)
$uri = "https://graph.microsoft.com/beta/servicePrincipals?`$expand=owners&`$top=$($Config.TestLimit)"
$response = Invoke-GraphRequest -Uri $uri

if ($response -and $response.value) {
    Write-Host "Successfully retrieved $($response.value.Count) service principals from beta endpoint" -ForegroundColor Green
    
    $publisherFoundCount = 0
    $testReport = @()
    
    foreach ($sp in $response.value) {
        # Get owner information (same as main script)
        $ownerInfo = Get-ServicePrincipalOwnerInfo -ServicePrincipal $sp
        
        # Create report entry (same structure as main script)
        $reportEntry = [PSCustomObject]@{
            DisplayName = if ($sp.displayName) { $sp.displayName } else { "N/A" }
            AppId = if ($sp.appId) { $sp.appId } else { "N/A" }
            ServicePrincipalType = if ($sp.servicePrincipalType) { $sp.servicePrincipalType } else { "N/A" }
            PublisherName = if ($sp.publisherName) { $sp.publisherName } else { "N/A" }
            AppOwnerOrganizationId = if ($sp.appOwnerOrganizationId) { $sp.appOwnerOrganizationId } else { "N/A" }
            OwnerNames = $ownerInfo.OwnerNamesString
            OwnerCount = $ownerInfo.OwnerCount.ToString()
        }
        
        $testReport += $reportEntry
        
        # Count publisher names found
        if ($sp.publisherName -and $sp.publisherName -ne "") {
            $publisherFoundCount++
        }
        
        # Display results
        Write-Host "`nService Principal: $($sp.displayName)" -ForegroundColor White
        Write-Host "  PublisherName: $($sp.publisherName)" -ForegroundColor $(if ($sp.publisherName) { "Green" } else { "Red" })
        Write-Host "  Type: $($sp.servicePrincipalType)" -ForegroundColor Gray
        Write-Host "  Owners: $($ownerInfo.OwnerCount)" -ForegroundColor Gray
    }
    
    Write-Host "`n=== RESULTS ===" -ForegroundColor Cyan
    Write-Host "Total service principals tested: $($response.value.Count)" -ForegroundColor White
    Write-Host "Publisher names found: $publisherFoundCount" -ForegroundColor Green
    Write-Host "Publisher names missing: $($response.value.Count - $publisherFoundCount)" -ForegroundColor Yellow
    Write-Host "Success rate: $([Math]::Round(($publisherFoundCount / $response.value.Count) * 100, 1))%" -ForegroundColor Green
    
    # Export test report to CSV
    $csvPath = "Test_PublisherName_Fix_Results_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
    $testReport | Export-Csv -Path $csvPath -NoTypeInformation
    Write-Host "`nTest report exported to: $csvPath" -ForegroundColor Green
    
    Write-Host "`n=== CONCLUSION ===" -ForegroundColor Cyan
    if ($publisherFoundCount -gt 0) {
        Write-Host "✓ FIX SUCCESSFUL: Beta endpoint provides PublisherName information" -ForegroundColor Green
        Write-Host "✓ Main script will now populate PublisherName in CSV reports" -ForegroundColor Green
        Write-Host "✓ No more 'N/A' values for applications with publisher information" -ForegroundColor Green
    } else {
        Write-Host "⚠ No publisher names found in this test sample" -ForegroundColor Yellow
        Write-Host "  This may be due to the specific service principals in the test set" -ForegroundColor Gray
        Write-Host "  The fix is still valid - beta endpoint provides more data than v1.0" -ForegroundColor Gray
    }
    
} else {
    Write-Host "Failed to retrieve service principals from beta endpoint" -ForegroundColor Red
}
