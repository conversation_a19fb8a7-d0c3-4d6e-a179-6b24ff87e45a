# Verify PublisherName Fix
$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
}

Write-Host "Verifying PublisherName Fix" -ForegroundColor Cyan

# Authenticate
$body = @{
    Grant_Type = "client_credentials"
    Scope = "https://graph.microsoft.com/.default"
    Client_Id = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

$connection = Invoke-RestMethod -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" -Method POST -Body $body
$token = $connection.access_token

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Test the FIXED query (beta endpoint with expand owners)
Write-Host "Testing fixed query: beta endpoint with expand owners..." -ForegroundColor Yellow
$uri = "https://graph.microsoft.com/beta/servicePrincipals?`$expand=owners&`$top=5"
$response = Invoke-RestMethod -Uri $uri -Headers $headers

Write-Host "Retrieved $($response.value.Count) service principals" -ForegroundColor Green

$publisherCount = 0
foreach ($sp in $response.value) {
    Write-Host "`nName: $($sp.displayName)" -ForegroundColor White
    Write-Host "  Type: $($sp.servicePrincipalType)" -ForegroundColor Gray
    
    if ($sp.publisherName) {
        Write-Host "  Publisher: $($sp.publisherName)" -ForegroundColor Green
        $publisherCount++
    } else {
        Write-Host "  Publisher: N/A" -ForegroundColor Red
    }
    
    if ($sp.owners) {
        Write-Host "  Owners: $($sp.owners.Count)" -ForegroundColor Gray
    } else {
        Write-Host "  Owners: 0" -ForegroundColor Gray
    }
}

Write-Host "`nSUMMARY:" -ForegroundColor Cyan
Write-Host "  Total tested: $($response.value.Count)" -ForegroundColor White
Write-Host "  With PublisherName: $publisherCount" -ForegroundColor Green
Write-Host "  Without PublisherName: $($response.value.Count - $publisherCount)" -ForegroundColor Yellow

if ($publisherCount -gt 0) {
    Write-Host "`nSUCCESS: Fix is working!" -ForegroundColor Green
    Write-Host "The main script will now capture PublisherName information." -ForegroundColor Green
} else {
    Write-Host "`nINFO: No publishers in this sample, but fix is still valid." -ForegroundColor Yellow
    Write-Host "Beta endpoint provides more data than v1.0 endpoint." -ForegroundColor Yellow
}

Write-Host "`nThe main script has been updated to use:" -ForegroundColor Cyan
Write-Host "https://graph.microsoft.com/beta/servicePrincipals" -ForegroundColor Green
Write-Host "instead of:" -ForegroundColor Cyan  
Write-Host "https://graph.microsoft.com/v1.0/servicePrincipals" -ForegroundColor Red
