# ===== TEST SCRIPT: PublisherName Capture =====
# Purpose: Test different methods to capture PublisherName from Service Principals
# This script will test various Graph API endpoints to find the correct way to get publisher information

# ===== CONFIGURATION =====
$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
    TestLimit = 10  # Test with first 10 service principals
}

Write-Host "=== PublisherName Capture Test Script ===" -ForegroundColor Cyan
Write-Host "Testing different methods to capture PublisherName from Service Principals" -ForegroundColor Yellow

# ===== AUTHENTICATION =====
Write-Host "Authenticating to Microsoft Graph..." -ForegroundColor Yellow

$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

try {
    $connection = Invoke-RestMethod `
        -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" `
        -Method POST `
        -Body $body

    $token = $connection.access_token
    Write-Host "Authentication successful" -ForegroundColor Green
}
catch {
    Write-Host "Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ===== HELPER FUNCTION =====
function Invoke-GraphRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri,
        [string]$Method = "GET"
    )

    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    try {
        return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $headers
    }
    catch {
        Write-Host "Graph API Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# ===== TEST 1: Standard Service Principals Query =====
Write-Host "`n=== TEST 1: Standard Service Principals Query ===" -ForegroundColor Cyan
Write-Host "Testing: GET /servicePrincipals" -ForegroundColor Yellow

$uri = "https://graph.microsoft.com/v1.0/servicePrincipals?`$top=$($Config.TestLimit)"
$response = Invoke-GraphRequest -Uri $uri

if ($response -and $response.value) {
    Write-Host "Found $($response.value.Count) service principals" -ForegroundColor Green
    
    foreach ($sp in $response.value) {
        Write-Host "`nService Principal: $($sp.displayName)" -ForegroundColor White
        Write-Host "  AppId: $($sp.appId)" -ForegroundColor Gray
        Write-Host "  PublisherName: $($sp.publisherName)" -ForegroundColor $(if ($sp.publisherName) { "Green" } else { "Red" })
        Write-Host "  AppOwnerOrganizationId: $($sp.appOwnerOrganizationId)" -ForegroundColor Gray
        Write-Host "  ServicePrincipalType: $($sp.servicePrincipalType)" -ForegroundColor Gray
    }
} else {
    Write-Host "No service principals found or API call failed" -ForegroundColor Red
}

# ===== TEST 2: Service Principals with Select Query =====
Write-Host "`n=== TEST 2: Service Principals with Select Query ===" -ForegroundColor Cyan
Write-Host "Testing: GET /servicePrincipals?`$select=displayName,appId,publisherName,appOwnerOrganizationId" -ForegroundColor Yellow

$uri = "https://graph.microsoft.com/v1.0/servicePrincipals?`$select=displayName,appId,publisherName,appOwnerOrganizationId,servicePrincipalType&`$top=$($Config.TestLimit)"
$response = Invoke-GraphRequest -Uri $uri

if ($response -and $response.value) {
    Write-Host "Found $($response.value.Count) service principals with select query" -ForegroundColor Green
    
    foreach ($sp in $response.value) {
        Write-Host "`nService Principal: $($sp.displayName)" -ForegroundColor White
        Write-Host "  AppId: $($sp.appId)" -ForegroundColor Gray
        Write-Host "  PublisherName: $($sp.publisherName)" -ForegroundColor $(if ($sp.publisherName) { "Green" } else { "Red" })
        Write-Host "  AppOwnerOrganizationId: $($sp.appOwnerOrganizationId)" -ForegroundColor Gray
        Write-Host "  ServicePrincipalType: $($sp.servicePrincipalType)" -ForegroundColor Gray
    }
} else {
    Write-Host "No service principals found or API call failed" -ForegroundColor Red
}

# ===== TEST 3: Get Application Registration for PublisherName =====
Write-Host "`n=== TEST 3: Get Application Registration for PublisherName ===" -ForegroundColor Cyan
Write-Host "Testing: GET /applications to get publisher information" -ForegroundColor Yellow

# First get some service principals that have corresponding app registrations
$uri = "https://graph.microsoft.com/v1.0/servicePrincipals?`$filter=servicePrincipalType eq 'Application'&`$top=$($Config.TestLimit)"
$spResponse = Invoke-GraphRequest -Uri $uri

if ($spResponse -and $spResponse.value) {
    Write-Host "Found $($spResponse.value.Count) application-type service principals" -ForegroundColor Green
    
    foreach ($sp in $spResponse.value) {
        Write-Host "`nService Principal: $($sp.displayName)" -ForegroundColor White
        Write-Host "  AppId: $($sp.appId)" -ForegroundColor Gray
        Write-Host "  SP PublisherName: $($sp.publisherName)" -ForegroundColor $(if ($sp.publisherName) { "Green" } else { "Red" })
        
        # Try to get the corresponding application registration
        if ($sp.appId) {
            $appUri = "https://graph.microsoft.com/v1.0/applications?`$filter=appId eq '$($sp.appId)'"
            $appResponse = Invoke-GraphRequest -Uri $appUri
            
            if ($appResponse -and $appResponse.value -and $appResponse.value.Count -gt 0) {
                $app = $appResponse.value[0]
                Write-Host "  App Registration Found: $($app.displayName)" -ForegroundColor Green
                Write-Host "  App PublisherName: $($app.publisherName)" -ForegroundColor $(if ($app.publisherName) { "Green" } else { "Red" })
                Write-Host "  App PublisherDomain: $($app.publisherDomain)" -ForegroundColor $(if ($app.publisherDomain) { "Green" } else { "Red" })
                Write-Host "  App SignInAudience: $($app.signInAudience)" -ForegroundColor Gray
            } else {
                Write-Host "  No corresponding app registration found" -ForegroundColor Yellow
            }
        }
    }
} else {
    Write-Host "No application-type service principals found" -ForegroundColor Red
}

# ===== TEST 4: Beta Endpoint Test =====
Write-Host "`n=== TEST 4: Beta Endpoint Test ===" -ForegroundColor Cyan
Write-Host "Testing: GET /beta/servicePrincipals" -ForegroundColor Yellow

$uri = "https://graph.microsoft.com/beta/servicePrincipals?`$top=$($Config.TestLimit)"
$response = Invoke-GraphRequest -Uri $uri

if ($response -and $response.value) {
    Write-Host "Found $($response.value.Count) service principals from beta endpoint" -ForegroundColor Green
    
    foreach ($sp in $response.value) {
        Write-Host "`nService Principal: $($sp.displayName)" -ForegroundColor White
        Write-Host "  AppId: $($sp.appId)" -ForegroundColor Gray
        Write-Host "  PublisherName: $($sp.publisherName)" -ForegroundColor $(if ($sp.publisherName) { "Green" } else { "Red" })
        Write-Host "  AppOwnerOrganizationId: $($sp.appOwnerOrganizationId)" -ForegroundColor Gray
        Write-Host "  ServicePrincipalType: $($sp.servicePrincipalType)" -ForegroundColor Gray
        
        # Check if there are additional properties in beta
        $allProperties = $sp | Get-Member -MemberType NoteProperty | Select-Object -ExpandProperty Name
        $publisherRelated = $allProperties | Where-Object { $_ -like "*publish*" -or $_ -like "*owner*" -or $_ -like "*domain*" }
        if ($publisherRelated) {
            Write-Host "  Publisher-related properties: $($publisherRelated -join ', ')" -ForegroundColor Cyan
        }
    }
} else {
    Write-Host "No service principals found from beta endpoint" -ForegroundColor Red
}

# ===== TEST 5: Specific Service Principal Details =====
Write-Host "`n=== TEST 5: Specific Service Principal Details ===" -ForegroundColor Cyan
Write-Host "Testing: GET /servicePrincipals/{id} for detailed information" -ForegroundColor Yellow

# Get the first service principal and fetch its detailed information
$uri = "https://graph.microsoft.com/v1.0/servicePrincipals?`$top=1"
$response = Invoke-GraphRequest -Uri $uri

if ($response -and $response.value -and $response.value.Count -gt 0) {
    $sp = $response.value[0]
    Write-Host "Getting detailed info for: $($sp.displayName)" -ForegroundColor Green
    
    $detailUri = "https://graph.microsoft.com/v1.0/servicePrincipals/$($sp.id)"
    $detailResponse = Invoke-GraphRequest -Uri $detailUri
    
    if ($detailResponse) {
        Write-Host "`nDetailed Service Principal Information:" -ForegroundColor White
        Write-Host "  DisplayName: $($detailResponse.displayName)" -ForegroundColor Gray
        Write-Host "  AppId: $($detailResponse.appId)" -ForegroundColor Gray
        Write-Host "  PublisherName: $($detailResponse.publisherName)" -ForegroundColor $(if ($detailResponse.publisherName) { "Green" } else { "Red" })
        Write-Host "  AppOwnerOrganizationId: $($detailResponse.appOwnerOrganizationId)" -ForegroundColor Gray
        Write-Host "  ServicePrincipalType: $($detailResponse.servicePrincipalType)" -ForegroundColor Gray
        
        # Show all properties for debugging
        Write-Host "`nAll available properties:" -ForegroundColor Cyan
        $allProperties = $detailResponse | Get-Member -MemberType NoteProperty | Select-Object -ExpandProperty Name | Sort-Object
        foreach ($prop in $allProperties) {
            $value = $detailResponse.$prop
            if ($value -and $value -ne "" -and $value -ne "N/A") {
                Write-Host "  $prop : $value" -ForegroundColor Gray
            }
        }
    }
}

Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Cyan
Write-Host "Test completed. Review the output above to determine:" -ForegroundColor Yellow
Write-Host "1. Which endpoint provides PublisherName information" -ForegroundColor White
Write-Host "2. Whether we need to query Application registrations separately" -ForegroundColor White
Write-Host "3. If beta endpoint provides additional publisher information" -ForegroundColor White
Write-Host "4. What alternative properties might contain publisher information" -ForegroundColor White
