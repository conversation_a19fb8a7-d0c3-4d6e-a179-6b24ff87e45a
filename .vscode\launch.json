{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "PowerShell: Launch Current File",
            "type": "PowerShell",
            "request": "launch",
            "script": "${file}",
            "cwd": "${fileDirname}",
            "args": [],
            "console": "integratedTerminal",
            "createTemporaryIntegratedConsole": false,
            "pwsh": {
                "bundledModulePath": ""
            }
        },
        {
            "name": "PowerShell: Launch Current File (Windows PowerShell)",
            "type": "PowerShell",
            "request": "launch",
            "script": "${file}",
            "cwd": "${fileDirname}",
            "args": [],
            "console": "integratedTerminal",
            "createTemporaryIntegratedConsole": false,
            "powershellExePath": "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe"
        }
    ]
}
