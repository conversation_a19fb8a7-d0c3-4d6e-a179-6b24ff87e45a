# Test Beta Endpoint for PublisherName
$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
}

Write-Host "Testing Beta Endpoint for PublisherName" -ForegroundColor Cyan

# Authenticate
$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

$connection = Invoke-RestMethod -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" -Method POST -Body $body
$token = $connection.access_token

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Test beta endpoint
Write-Host "Querying beta endpoint..." -ForegroundColor Yellow
$uri = "https://graph.microsoft.com/beta/servicePrincipals?`$filter=servicePrincipalType eq 'Application'&`$top=10"
$response = Invoke-RestMethod -Uri $uri -Headers $headers

Write-Host "Found $($response.value.Count) service principals" -ForegroundColor Green

$publisherCount = 0
foreach ($sp in $response.value) {
    Write-Host "Name: $($sp.displayName)" -ForegroundColor White
    if ($sp.publisherName) {
        Write-Host "  Publisher: $($sp.publisherName)" -ForegroundColor Green
        $publisherCount++
    } else {
        Write-Host "  Publisher: N/A" -ForegroundColor Red
    }
}

Write-Host "`nSUMMARY: $publisherCount out of $($response.value.Count) have publisher names" -ForegroundColor Cyan
Write-Host "SOLUTION: Change main script to use beta endpoint" -ForegroundColor Green
