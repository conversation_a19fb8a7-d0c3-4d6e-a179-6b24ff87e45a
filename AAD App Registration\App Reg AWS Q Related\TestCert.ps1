# === Configuration ===
$tenantId   = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
$clientId   = "3152d53e-eeac-44a7-a0c9-0be4ac013988"
$pfxPath    = "$HOME\Downloads\ProjectSiteProvision.pfx" #Cert with password
$pfxPath2    = "$HOME\Downloads\ProjectSiteProvisioning2.pfx" #Cert without password
$pfxPassword = ConvertTo-SecureString "Jabil12345" -AsPlainText -Force

# === Helper Functions ===
function ConvertTo-Base64Url {
    param([byte[]]$bytes)
    $base64 = [Convert]::ToBase64String($bytes)
    $base64Url = $base64.Replace('+', '-').Replace('/', '_').TrimEnd('=')
    return $base64Url
}

# === Load Certificate ===
Write-Host "Loading certificate from: $pfxPath"
try {
    # Use constructor instead of Import method for .NET Core compatibility
    $keyStorageFlags = [System.Security.Cryptography.X509Certificates.X509KeyStorageFlags]::Exportable -bor
                      [System.Security.Cryptography.X509Certificates.X509KeyStorageFlags]::PersistKeySet

    $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2($pfxPath, $pfxPassword, $keyStorageFlags)

    Write-Host "✅ Certificate loaded successfully"
    Write-Host "   Subject: $($cert.Subject)"
    Write-Host "   Thumbprint: $($cert.Thumbprint)"
    Write-Host "   Valid from: $($cert.NotBefore)"
    Write-Host "   Valid to: $($cert.NotAfter)"
}
catch {
    Write-Error "Failed to load certificate: $($_.Exception.Message)"
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Verify the certificate file exists at: $pfxPath" -ForegroundColor Yellow
    Write-Host "2. Check if the password 'Jabil12345' is correct" -ForegroundColor Yellow
    Write-Host "3. Ensure the .pfx file is not corrupted" -ForegroundColor Yellow
    exit 1
}

if (-not $cert.HasPrivateKey) {
    Write-Error "Certificate does not have a private key."
    exit 1
}

# === Create JWT for Client Assertion ===
Write-Host "Creating JWT client assertion..."

# JWT Header
$jwtHeader = @{
    alg = "RS256"
    typ = "JWT"
    x5t = ConvertTo-Base64Url -bytes $cert.GetCertHash()
} | ConvertTo-Json -Compress

# JWT Payload
$now = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
$jwtPayload = @{
    aud = "https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token"
    exp = $now + 600  # 10 minutes from now
    iss = $clientId
    jti = [Guid]::NewGuid().ToString()
    nbf = $now
    sub = $clientId
} | ConvertTo-Json -Compress

# Encode header and payload
$encodedHeader = ConvertTo-Base64Url -bytes ([System.Text.Encoding]::UTF8.GetBytes($jwtHeader))
$encodedPayload = ConvertTo-Base64Url -bytes ([System.Text.Encoding]::UTF8.GetBytes($jwtPayload))
$jwtToSign = "$encodedHeader.$encodedPayload"

# Sign JWT
try {
    $privateKey = [System.Security.Cryptography.RSACryptoServiceProvider]$cert.PrivateKey
    $signatureBytes = $privateKey.SignData(
        [System.Text.Encoding]::UTF8.GetBytes($jwtToSign),
        [System.Security.Cryptography.HashAlgorithmName]::SHA256,
        [System.Security.Cryptography.RSASignaturePadding]::Pkcs1
    )
    $encodedSignature = ConvertTo-Base64Url -bytes $signatureBytes
    $clientAssertion = "$jwtToSign.$encodedSignature"
    Write-Host "✅ JWT client assertion created successfully"
}
catch {
    Write-Error "Failed to sign JWT: $($_.Exception.Message)"
    exit 1
}

# === Request Access Token ===
Write-Host "Requesting access token from Microsoft Graph..."
try {
    $tokenResponse = Invoke-RestMethod -Method Post `
        -Uri "https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token" `
        -Body @{
            client_id             = $clientId
            scope                 = "https://graph.microsoft.com/.default"
            client_assertion_type = "urn:ietf:params:oauth:client-assertion-type:jwt-bearer"
            client_assertion      = $clientAssertion
            grant_type            = "client_credentials"
        } `
        -ContentType "application/x-www-form-urlencoded"

    $accessToken = $tokenResponse.access_token
    Write-Host "✅ Access token acquired successfully"
    Write-Host "   Token type: $($tokenResponse.token_type)"
    Write-Host "   Expires in: $($tokenResponse.expires_in) seconds"
}
catch {
    Write-Error "Failed to acquire access token: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error details: $errorBody" -ForegroundColor Red
    }
    exit 1
}

# === Test Graph API Connection ===
Write-Host "`nTesting Graph API connection..."
try {
    # Test 1: Get organization information
    Write-Host "Test 1: Getting organization information..."
    $orgResponse = Invoke-RestMethod -Method Get `
        -Uri "https://graph.microsoft.com/v1.0/organization" `
        -Headers @{ Authorization = "Bearer $accessToken" }

    Write-Host "✅ Organization API call succeeded"
    Write-Host "   Organization: $($orgResponse.value[0].displayName)"
    Write-Host "   Tenant ID: $($orgResponse.value[0].id)"

    # Test 2: Get application information
    Write-Host "`nTest 2: Getting application information..."
    $appResponse = Invoke-RestMethod -Method Get `
        -Uri "https://graph.microsoft.com/v1.0/applications?`$filter=appId eq '$clientId'" `
        -Headers @{ Authorization = "Bearer $accessToken" }

    if ($appResponse.value.Count -gt 0) {
        Write-Host "✅ Application API call succeeded"
        Write-Host "   App Name: $($appResponse.value[0].displayName)"
        Write-Host "   App ID: $($appResponse.value[0].appId)"
    } else {
        Write-Host "⚠️  Application not found or insufficient permissions"
    }

    # Test 3: Try to access SharePoint sites (if permissions allow)
    Write-Host "`nTest 3: Testing SharePoint sites access..."
    try {
        $sitesResponse = Invoke-RestMethod -Method Get `
            -Uri "https://graph.microsoft.com/v1.0/sites/root" `
            -Headers @{ Authorization = "Bearer $accessToken" }

        Write-Host "✅ SharePoint sites API call succeeded"
        Write-Host "   Site: $($sitesResponse.displayName)"
        Write-Host "   URL: $($sitesResponse.webUrl)"
    }
    catch {
        Write-Host "⚠️  SharePoint sites access failed (may need Sites.Read.All permission): $($_.Exception.Message)" -ForegroundColor Yellow
    }

    Write-Host "`n🎉 Certificate authentication test completed successfully!" -ForegroundColor Green
}
catch {
    Write-Error "Graph API test failed: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error details: $errorBody" -ForegroundColor Red
    }
    exit 1
}
