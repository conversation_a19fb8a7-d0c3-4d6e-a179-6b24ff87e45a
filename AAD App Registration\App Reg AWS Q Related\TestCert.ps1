# === Configuration ===
$tenantId   = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
$clientId   = "3152d53e-eeac-44a7-a0c9-0be4ac013988"
$pfxPath    = "$HOME\Downloads\ProjectSiteProvision.pfx"
$pfxPassword = ConvertTo-SecureString "Jabil12345" -AsPlainText -Force

# Load cert
$pfxPassword = ConvertTo-SecureString "Jabil12345" -AsPlainText -Force
$cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
$cert.Import($pfxPath, $pfxPassword,
    [System.Security.Cryptography.X509Certificates.X509KeyStorageFlags]::Exportable `
    -bor [System.Security.Cryptography.X509Certificates.X509KeyStorageFlags]::PersistKeySet)

if (-not $cert.HasPrivateKey) {
    Write-Error "Certificate does not have a private key."
    exit
}

# Get private key with correct type
$privateKey = [System.Security.Cryptography.RSACryptoServiceProvider]$cert.PrivateKey

# Sign JWT
$signatureBytes = $privateKey.SignData(
    [System.Text.Encoding]::UTF8.GetBytes($jwtToSign),
    (New-Object System.Security.Cryptography.SHA256CryptoServiceProvider)
)


$encodedSignature = & $Base64UrlEncode $signatureBytes
$clientAssertion = "$jwtToSign.$encodedSignature"

# === Request Access Token ===
$tokenResponse = Invoke-RestMethod -Method Post `
    -Uri "https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token" `
    -Body @{
        client_id             = $clientId
        scope                 = "https://graph.microsoft.com/.default"
        client_assertion_type = "urn:ietf:params:oauth:client-assertion-type:jwt-bearer"
        client_assertion      = $clientAssertion
        grant_type            = "client_credentials"
    } `
    -ContentType "application/x-www-form-urlencoded"

$accessToken = $tokenResponse.access_token
Write-Host "✅ Access token acquired."

# === Make test Graph API call ===
$response = Invoke-RestMethod -Method Get `
    -Uri "https://graph.microsoft.com/v1.0/sites/root" `
    -Headers @{ Authorization = "Bearer $accessToken" }

Write-Host "✅ Graph API call succeeded:"
$response
