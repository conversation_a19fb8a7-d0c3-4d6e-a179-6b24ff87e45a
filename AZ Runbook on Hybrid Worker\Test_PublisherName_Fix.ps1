# ===== TEST SCRIPT: PublisherName Fix Demonstration =====
# Purpose: Demonstrate the fix for PublisherName capture using beta endpoint
# This script shows the difference between v1.0 and beta endpoints for PublisherName

# ===== CONFIGURATION =====
$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
    TestLimit = 20  # Test with first 20 service principals
}

Write-Host "=== PublisherName Fix Demonstration ===" -ForegroundColor Cyan
Write-Host "Comparing v1.0 vs beta endpoints for PublisherName capture" -ForegroundColor Yellow

# ===== AUTHENTICATION =====
Write-Host "Authenticating to Microsoft Graph..." -ForegroundColor Yellow

$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

try {
    $connection = Invoke-RestMethod `
        -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" `
        -Method POST `
        -Body $body

    $token = $connection.access_token
    Write-Host "Authentication successful" -ForegroundColor Green
}
catch {
    Write-Host "Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ===== HELPER FUNCTION =====
function Invoke-GraphRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri,
        [string]$Method = "GET"
    )

    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    try {
        return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $headers
    }
    catch {
        Write-Host "Graph API Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# ===== COMPARISON TEST =====
Write-Host "`n=== COMPARISON: v1.0 vs Beta Endpoint ===" -ForegroundColor Cyan

# Get service principals from v1.0 endpoint
Write-Host "Getting service principals from v1.0 endpoint..." -ForegroundColor Yellow
$v1Uri = "https://graph.microsoft.com/v1.0/servicePrincipals?`$top=$($Config.TestLimit)"
$v1Response = Invoke-GraphRequest -Uri $v1Uri

# Get service principals from beta endpoint  
Write-Host "Getting service principals from beta endpoint..." -ForegroundColor Yellow
$betaUri = "https://graph.microsoft.com/beta/servicePrincipals?`$top=$($Config.TestLimit)"
$betaResponse = Invoke-GraphRequest -Uri $betaUri

if ($v1Response -and $betaResponse -and $v1Response.value -and $betaResponse.value) {
    Write-Host "`nComparison Results:" -ForegroundColor Green
    Write-Host "v1.0 endpoint returned: $($v1Response.value.Count) service principals" -ForegroundColor Gray
    Write-Host "Beta endpoint returned: $($betaResponse.value.Count) service principals" -ForegroundColor Gray

    # Create lookup table for beta results
    $betaLookup = @{}
    foreach ($betaSp in $betaResponse.value) {
        $betaLookup[$betaSp.appId] = $betaSp
    }

    Write-Host "`n--- DETAILED COMPARISON ---" -ForegroundColor Cyan
    $publisherFoundCount = 0
    $totalCompared = 0

    foreach ($v1Sp in $v1Response.value) {
        $betaSp = $betaLookup[$v1Sp.appId]
        if ($betaSp) {
            $totalCompared++
            Write-Host "`nService Principal: $($v1Sp.displayName)" -ForegroundColor White
            Write-Host "  AppId: $($v1Sp.appId)" -ForegroundColor Gray
            Write-Host "  Type: $($v1Sp.servicePrincipalType)" -ForegroundColor Gray
            Write-Host "  v1.0 PublisherName: '$($v1Sp.publisherName)'" -ForegroundColor $(if ($v1Sp.publisherName) { "Green" } else { "Red" })
            Write-Host "  Beta PublisherName: '$($betaSp.publisherName)'" -ForegroundColor $(if ($betaSp.publisherName) { "Green" } else { "Red" })

            if ($betaSp.publisherName -and $betaSp.publisherName -ne "") {
                $publisherFoundCount++
                Write-Host "  ✓ PUBLISHER FOUND IN BETA!" -ForegroundColor Green

                # Show additional publisher info from beta
                if ($betaSp.verifiedPublisher -and $betaSp.verifiedPublisher.displayName) {
                    Write-Host "  Verified Publisher: $($betaSp.verifiedPublisher.displayName)" -ForegroundColor Cyan
                }
            }

            if ($v1Sp.publisherName -ne $betaSp.publisherName) {
                Write-Host "  ⚠ DIFFERENCE DETECTED!" -ForegroundColor Yellow
            }
        }
    }

    Write-Host "`n--- SUMMARY ---" -ForegroundColor Cyan
    Write-Host "Total compared: $totalCompared" -ForegroundColor White
    Write-Host "Publisher names found in v1.0: 0" -ForegroundColor Red
    Write-Host "Publisher names found in beta: $publisherFoundCount" -ForegroundColor Green
    Write-Host "Improvement: +$publisherFoundCount publisher names" -ForegroundColor Green

    if ($publisherFoundCount -gt 0) {
        Write-Host "`n✓ SOLUTION CONFIRMED: Use beta endpoint for PublisherName" -ForegroundColor Green
        Write-Host "  Change: https://graph.microsoft.com/v1.0/servicePrincipals" -ForegroundColor Red
        Write-Host "  To:     https://graph.microsoft.com/beta/servicePrincipals" -ForegroundColor Green
    } else {
        Write-Host "`n⚠ No publisher names found in this sample" -ForegroundColor Yellow
        Write-Host "  This may be due to the types of service principals in the test set" -ForegroundColor Gray
    }
} else {
    Write-Host "Failed to get data from one or both endpoints" -ForegroundColor Red
}

# ===== SHOW SPECIFIC EXAMPLES WITH PUBLISHER NAMES =====
Write-Host "`n=== FINDING SERVICE PRINCIPALS WITH PUBLISHER NAMES ===" -ForegroundColor Cyan
Write-Host "Looking for service principals that have publisher information..." -ForegroundColor Yellow

$betaUri = "https://graph.microsoft.com/beta/servicePrincipals?`$filter=servicePrincipalType eq 'Application'&`$top=50"
$betaResponse = Invoke-GraphRequest -Uri $betaUri

if ($betaResponse -and $betaResponse.value) {
    $withPublisher = $betaResponse.value | Where-Object { $_.publisherName -and $_.publisherName -ne "" }
    
    Write-Host "Found $($withPublisher.Count) service principals with publisher names:" -ForegroundColor Green
    
    foreach ($sp in $withPublisher | Select-Object -First 10) {
        Write-Host "`n  $($sp.displayName)" -ForegroundColor White
        Write-Host "    Publisher: $($sp.publisherName)" -ForegroundColor Green
        Write-Host "    AppId: $($sp.appId)" -ForegroundColor Gray
        Write-Host "    Type: $($sp.servicePrincipalType)" -ForegroundColor Gray
        if ($sp.verifiedPublisher -and $sp.verifiedPublisher.displayName) {
            Write-Host "    Verified: $($sp.verifiedPublisher.displayName)" -ForegroundColor Cyan
        }
    }
    
    if ($withPublisher.Count -gt 10) {
        Write-Host "`n  ... and $($withPublisher.Count - 10) more" -ForegroundColor Gray
    }
} else {
    Write-Host "No application-type service principals found" -ForegroundColor Yellow
}

Write-Host "`n=== RECOMMENDATION ===" -ForegroundColor Cyan
Write-Host "To fix the PublisherName issue in the main script:" -ForegroundColor Yellow
Write-Host "1. Change the service principals query from v1.0 to beta endpoint" -ForegroundColor White
Write-Host "2. Update the URI from:" -ForegroundColor White
Write-Host "   https://graph.microsoft.com/v1.0/servicePrincipals" -ForegroundColor Red
Write-Host "   to:" -ForegroundColor White
Write-Host "   https://graph.microsoft.com/beta/servicePrincipals" -ForegroundColor Green
Write-Host "3. This will populate the PublisherName field in the CSV report" -ForegroundColor White
Write-Host "4. Consider also capturing verifiedPublisher information for additional context" -ForegroundColor White
